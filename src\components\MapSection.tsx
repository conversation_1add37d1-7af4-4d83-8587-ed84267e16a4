'use client'

import { useState } from 'react'
import { summits, Summit } from '@/data/summits'
import { MapPin, Mountain, Navigation } from 'lucide-react'

export default function MapSection() {
  const [selectedSummit, setSelectedSummit] = useState<Summit | null>(null)

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Peta Lokasi
            <span className="block text-orange-500">7 Summits</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Temukan lokasi persebaran 7 gunung tertinggi Indonesia dari Sabang hingga Merauke. 
            Klik pada setiap marker untuk melihat informasi detail.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Map Container */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              {/* Embedded Map */}
              <div className="relative h-96 bg-gray-200">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d16094234.192905806!2d95.2930261!3d-2.5489450000000004!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x2c4c07d7496404b7%3A0xe37b4de71badf485!2sIndonesia!5e0!3m2!1sen!2sid!4v1703123456789!5m2!1sen!2sid"
                  width="100%"
                  height="100%"
                  style={{ border: 0 }}
                  allowFullScreen
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  className="rounded-t-xl"
                ></iframe>
                
                {/* Custom Markers Overlay */}
                <div className="absolute inset-0 pointer-events-none">
                  {summits.map((summit, index) => (
                    <div
                      key={summit.id}
                      className="absolute pointer-events-auto"
                      style={{
                        left: `${20 + (index * 12)}%`,
                        top: `${30 + (index % 3) * 20}%`,
                      }}
                    >
                      <button
                        onClick={() => setSelectedSummit(summit)}
                        className="bg-orange-500 text-white p-2 rounded-full shadow-lg hover:bg-orange-600 transition-colors duration-200 transform hover:scale-110"
                        title={summit.name}
                      >
                        <Mountain size={16} />
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Map Legend */}
              <div className="p-4 bg-white border-t">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Mountain className="text-orange-500" size={20} />
                    <span className="text-sm text-gray-600">Lokasi 7 Summits Indonesia</span>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <div className="flex items-center gap-1">
                      <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                      <span>Gunung</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Summit List */}
          <div className="space-y-4">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Daftar Lokasi</h3>
            
            {summits.map((summit) => (
              <div
                key={summit.id}
                className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                  selectedSummit?.id === summit.id
                    ? 'border-orange-500 bg-orange-50'
                    : 'border-gray-200 bg-white hover:border-orange-300'
                }`}
                onClick={() => setSelectedSummit(summit)}
              >
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center">
                      <Mountain size={16} />
                    </div>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 mb-1">{summit.name}</h4>
                    <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                      <MapPin size={14} />
                      <span>{summit.location}</span>
                    </div>
                    <div className="text-sm text-gray-500">
                      {summit.height.toLocaleString()}m • {summit.difficulty}
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {/* Selected Summit Details */}
            {selectedSummit && (
              <div className="mt-6 p-6 bg-white rounded-lg shadow-lg border border-orange-200">
                <h4 className="text-xl font-bold text-gray-900 mb-3">{selectedSummit.name}</h4>
                <p className="text-gray-700 text-sm mb-4">{selectedSummit.description}</p>
                
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <Navigation size={14} className="text-orange-500" />
                    <span><strong>Koordinat:</strong> {selectedSummit.coordinates.lat}, {selectedSummit.coordinates.lng}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Mountain size={14} className="text-orange-500" />
                    <span><strong>Ketinggian:</strong> {selectedSummit.height.toLocaleString()}m</span>
                  </div>
                </div>

                <button className="w-full mt-4 bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded-lg transition-colors duration-200 font-semibold">
                  Lihat di Google Maps
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  )
}
