'use client'

import { useState } from 'react'
import { ImageIcon, Mountain } from 'lucide-react'

interface ImageWithFallbackProps {
  src: string
  alt: string
  className?: string
  fallbackSrc?: string
  showSkeleton?: boolean
  skeletonIcon?: React.ReactNode
  onLoad?: () => void
  onError?: () => void
}

export default function ImageWithFallback({
  src,
  alt,
  className = '',
  fallbackSrc = 'https://picsum.photos/800/600?random=99',
  showSkeleton = true,
  skeletonIcon,
  onLoad,
  onError
}: ImageWithFallbackProps) {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(false)

  const handleLoad = () => {
    console.log('✅ ImageWithFallback: Image loaded successfully:', src)
    setLoading(false)
    setError(false)
    onLoad?.()
  }

  const handleError = () => {
    console.error('❌ ImageWithFallback: Image failed to load:', src)
    setLoading(false)
    setError(true)
    onError?.()
  }

  const handleFallbackError = () => {
    setLoading(false)
    // If fallback also fails, we'll show the error state
  }

  return (
    <div className="relative w-full h-full">
      {/* Loading Skeleton */}
      {loading && showSkeleton && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
          <div className="text-gray-400">
            {skeletonIcon || <ImageIcon size={32} />}
          </div>
        </div>
      )}

      {/* Error State */}
      {error && !loading && (
        <div className="absolute inset-0 bg-gradient-to-br from-gray-300 to-gray-400 flex items-center justify-center">
          <div className="text-center text-gray-600">
            <Mountain size={32} className="mx-auto mb-2" />
            <p className="text-sm font-medium">Image not available</p>
          </div>
        </div>
      )}

      {/* Main Image */}
      {!error && (
        <img
          src={src}
          alt={alt}
          className={`${className} ${loading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
          onLoad={handleLoad}
          onError={handleError}
          loading="lazy"
        />
      )}

      {/* Fallback Image */}
      {error && fallbackSrc && fallbackSrc !== src && (
        <img
          src={fallbackSrc}
          alt={`Fallback: ${alt}`}
          className={`${className} absolute inset-0`}
          onLoad={handleLoad}
          onError={handleFallbackError}
          loading="lazy"
        />
      )}
    </div>
  )
}
