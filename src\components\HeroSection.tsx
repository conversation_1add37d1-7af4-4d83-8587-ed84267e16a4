'use client'

import { ChevronDown } from 'lucide-react'

export default function HeroSection() {
  const scrollToSummits = () => {
    const summitsSection = document.getElementById('summits')
    if (summitsSection) {
      summitsSection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section id="hero" className="relative flex items-center justify-center overflow-hidden" style={{ height: 'calc(100vh - 2.5rem)', minHeight: '600px' }}>
      {/* Background Image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url('https://images.unsplash.com/photo-1506197603052-3cc9c3a201bd?w=1920&q=80')`,
        }}
      >
        {/* Dark overlay */}
        <div className="absolute inset-0 bg-black/45"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center text-white px-4 max-w-4xl mx-auto" style={{ marginTop: '-2rem' }}>
        <h1 className="text-5xl md:text-7xl font-bold leading-tight" style={{ marginBottom: '1.5rem' }}>
          7 Summits
          <span className="block text-4xl md:text-6xl text-orange-400" style={{ marginTop: '0.5rem' }}>
            Indonesia
          </span>
        </h1>
        
        <p className="text-xl md:text-2xl text-gray-200 max-w-2xl mx-auto leading-relaxed" style={{ marginBottom: '2rem' }}>
          Jelajahi puncak-puncak tertinggi Nusantara. Dari Puncak Jaya hingga Gunung Slamet,
          temukan petualangan yang tak terlupakan di 7 gunung spektakuler Indonesia.
        </p>

        <div className="flex flex-col sm:flex-row justify-center items-center" style={{ gap: '1rem' }}>
          <button
            onClick={scrollToSummits}
            className="bg-orange-500 hover:bg-orange-600 text-white rounded-lg text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg"
            style={{ padding: '0.875rem 2rem' }}
          >
            Mulai Petualangan
          </button>

          <button
            className="border-2 border-white text-white hover:bg-white hover:text-gray-900 rounded-lg text-lg font-semibold transition-all duration-300"
            style={{ padding: '0.875rem 2rem' }}
          >
            Pelajari Lebih Lanjut
          </button>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute w-full flex justify-center" style={{ bottom: '2rem' }}>
        <button onClick={scrollToSummits} className="flex flex-col items-center text-white animate-bounce">
          <span className="text-sm" style={{ marginBottom: '0.5rem' }}>Scroll untuk melihat</span>
          <ChevronDown size={20} />
        </button>
      </div>

      {/* Decorative elements */}
      <div className="absolute left-10 border border-white/20 rounded-full hidden lg:block" style={{ top: '5rem', width: '5rem', height: '5rem' }}></div>
      <div className="absolute right-10 border border-orange-400/30 rounded-full hidden lg:block" style={{ bottom: '5rem', width: '4rem', height: '4rem' }}></div>
      <div className="absolute top-1/2 right-20 border border-white/10 rounded-full hidden lg:block" style={{ width: '3rem', height: '3rem' }}></div>
    </section>
  )
}
