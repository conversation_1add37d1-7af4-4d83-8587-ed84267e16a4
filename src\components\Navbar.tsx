'use client'

import { useState, useEffect } from 'react'
import { Menu, X, Mountain } from 'lucide-react'

export default function Navbar() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [activeSection, setActiveSection] = useState('hero')

  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY
      setIsScrolled(scrollY > 50)

      const sections = ['hero', 'summits', 'gallery', 'climbing-info', 'contact']
      const scrollPosition = scrollY + 100

      for (const section of sections) {
        const element = document.getElementById(section)
        if (element) {
          const offsetTop = element.offsetTop
          const offsetBottom = offsetTop + element.offsetHeight

          if (scrollPosition >= offsetTop && scrollPosition < offsetBottom) {
            setActiveSection(section)
            break
          }
        }
      }
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
      setIsMobileMenuOpen(false)
    }
  }

  const menuItems = [
    { name: 'Beranda', href: '#hero', section: 'hero' },
    { name: 'Gunung', href: '#summits', section: 'summits' },
    { name: 'Galeri', href: '#gallery', section: 'gallery' },
    { name: 'Tentang', href: '#climbing-info', section: 'climbing-info' },
    { name: 'Kontak', href: '#contact', section: 'contact' },
  ]

  return (
    <>
      <nav
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ease-out ${
          isScrolled
            ? 'bg-white/90 shadow-2xl border-b border-gray-200/50'
            : 'bg-transparent'
        }`}
        style={{
          backdropFilter: 'blur(16px)',
          WebkitBackdropFilter: 'blur(16px)',
          willChange: 'backdrop-filter',
        }}
      >
        <div className="container mx-auto px-4 lg:px-8">
          <div className="flex items-center justify-between" style={{ height: '4rem' }}>
            {/* Logo */}
            <div
              className="flex items-center space-x-3 cursor-pointer group"
              onClick={() => scrollToSection('hero')}
            >
              <div className="bg-orange-500 rounded transition-all duration-300 group-hover:scale-110 group-hover:bg-orange-600" style={{ padding: '0.5rem', maxHeight: '2.5rem' }}>
                <Mountain className="text-white" style={{ width: '1.25rem', height: '1.25rem' }} />
              </div>
              <div className="flex flex-col" style={{ maxHeight: '40px' }}>
                <span className={`font-bold transition-colors duration-300 font-poppins ${isScrolled ? 'text-gray-900' : 'text-white drop-shadow-lg'}`} style={{ fontSize: '1rem', lineHeight: '1.2' }}>
                  7 Summits
                </span>
                <span className={`font-medium transition-colors duration-300 font-poppins ${isScrolled ? 'text-orange-600' : 'text-orange-200'}`} style={{ fontSize: '0.75rem', lineHeight: '1.1' }}>
                  Indonesia
                </span>
              </div>
            </div>

            {/* Desktop Menu */}
            <div className="hidden lg:flex items-center" style={{ gap: '1.5rem' }}>
              {menuItems.map((item) => (
                <button
                  key={item.name}
                  onClick={() => scrollToSection(item.section)}
                  className={`font-medium transition-all duration-300 hover:scale-105 font-poppins relative ${
                    activeSection === item.section
                      ? isScrolled
                        ? 'text-orange-600 font-semibold'
                        : 'text-orange-300 font-semibold'
                      : isScrolled
                        ? 'text-gray-700 hover:text-orange-600'
                        : 'text-white hover:text-orange-300 drop-shadow-sm'
                  }`}
                  style={{ fontSize: '0.95rem' }}
                >
                  {item.name}
                  {activeSection === item.section && (
                    <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-orange-500 rounded-full"></div>
                  )}
                </button>
              ))}

              {/* CTA Button */}
              <button
                onClick={() => scrollToSection('contact')}
                className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white rounded-full font-semibold transition-all duration-300 transform hover:scale-105 font-poppins"
                style={{ padding: '6px 14px', fontSize: '0.875rem' }}
              >
                Mulai Petualangan
              </button>
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className={`lg:hidden rounded transition-colors duration-300 ${
                isScrolled
                  ? 'text-gray-700 hover:bg-gray-100'
                  : 'text-white hover:bg-white/10'
              }`}
              style={{ padding: '0.5rem' }}
            >
              {isMobileMenuOpen ? (
                <X style={{ width: '1.25rem', height: '1.25rem' }} />
              ) : (
                <Menu style={{ width: '1.25rem', height: '1.25rem' }} />
              )}
            </button>
          </div>

          {/* Mobile Menu */}
          <div
            className={`lg:hidden transition-all duration-500 ease-out overflow-hidden ${
              isMobileMenuOpen ? 'opacity-100' : 'max-h-0 opacity-0'
            }`}
            style={{ maxHeight: isMobileMenuOpen ? '20rem' : '0' }}
          >
            <div
              className="bg-white/90 backdrop-blur-xl rounded-b-lg shadow-2xl border-t border-gray-200/50"
              style={{
                padding: '1rem 0',
                backdropFilter: 'blur(16px)',
                WebkitBackdropFilter: 'blur(16px)',
              }}
            >
              {menuItems.map((item, index) => (
                <button
                  key={item.name}
                  onClick={() => scrollToSection(item.section)}
                  className="block w-full text-left text-gray-700 hover:text-orange-600 hover:bg-orange-50 transition-all duration-300 font-medium font-poppins"
                  style={{
                    padding: '0.75rem 1rem',
                    fontSize: '0.95rem',
                    animationDelay: `${index * 50}ms`,
                  }}
                >
                  {item.name}
                </button>
              ))}

              {/* Mobile CTA Button */}
              <div style={{ padding: '0.75rem 1rem' }}>
                <button
                  onClick={() => scrollToSection('contact')}
                  className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white rounded-full font-semibold transition-all duration-300 transform hover:scale-105 font-poppins"
                  style={{ padding: '6px 14px', fontSize: '0.875rem' }}
                >
                  Mulai Petualangan
                </button>
              </div>
            </div>
          </div>
        </div>
      </nav>
    </>
  )
}
